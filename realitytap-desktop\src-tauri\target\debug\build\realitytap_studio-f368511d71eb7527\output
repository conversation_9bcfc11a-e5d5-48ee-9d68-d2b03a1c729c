cargo:rustc-env=BUILD_DATE=2025-08-01 07:46:20 UTC
cargo:rustc-env=TARGET_ARCH=x86_64
cargo:rustc-env=TARGET_OS=windows
cargo:warning=Setting target info: windows-x86_64
cargo:warning=ffmpeg source not found: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/ffmpeg/windows/x64/ffmpeg.exe
cargo:warning=ffprobe source not found: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/ffmpeg/windows/x64/ffprobe.exe
cargo:rerun-if-changed=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/ffmpeg/windows/x64
cargo:warning=Copied librtcore.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/librtcore.dll
cargo:warning=Copied librtssl.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/librtssl.dll
cargo:warning=Copied librtutils.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/librtutils.dll
cargo:warning=Copied libgcc_s_seh-1.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/libgcc_s_seh-1.dll
cargo:warning=Copied libstdc++-6.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/libstdc++-6.dll
cargo:warning=Copied libwinpthread-1.dll to root: E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/libwinpthread-1.dll
cargo:rerun-if-changed=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri/libs/windows/x64
cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=desktop
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_awa_realitytap
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
cargo:rerun-if-changed=libgcc_s_seh-1.dll
cargo:rerun-if-changed=librtcore.dll
cargo:rerun-if-changed=librtssl.dll
cargo:rerun-if-changed=librtutils.dll
cargo:rerun-if-changed=libstdc++-6.dll
cargo:rerun-if-changed=libwinpthread-1.dll
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=E:\03.Codes\10.AWA\10.realitytap\realitytap-ecosystem\realitytap-desktop\src-tauri\target\debug\build\realitytap_studio-f368511d71eb7527\out\resource.lib
